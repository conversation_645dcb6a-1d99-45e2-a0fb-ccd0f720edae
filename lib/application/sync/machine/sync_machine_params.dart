import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/translator.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/util/duration_tracker.dart';

class SyncMachineParams {
  final AppConfig appConfig;
  final AuthRepository authRepository;
  final Session session;
  final Organization organization;
  final Repository db;
  final Translator apiTranslator;
  final ApiHelper apiHelper;
  final SyncState syncState;
  final AnalyticsLogger analyticsLogger;

  final DurationTracker durationTracker = DurationTracker();

  bool needsProjectResync = false;

  SyncMachineParams({
    required this.appConfig,
    required this.authRepository,
    required this.session,
    required this.organization,
    required this.db,
    required this.apiTranslator,
    required this.apiHelper,
    required this.syncState,
    required this.analyticsLogger,
  });
}
