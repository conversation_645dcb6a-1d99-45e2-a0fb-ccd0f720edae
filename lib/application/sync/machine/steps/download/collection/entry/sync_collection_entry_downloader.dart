import 'package:bitacora/application/sync/machine/steps/download/collection/entry/has_existing_project_for_entry_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/entry/entry_by_remote_id_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/entry/has_existing_outgoing_mutation_for_downloaded_entry_repository_query.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';
import 'package:bitacora/util/logger/logger.dart';

class SyncCollectionEntryDownloader extends SyncCollectionDownloader {
  SyncCollectionEntryDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.entry;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    map['organization_id'] = organization.remoteId!.apiValue;
    final entry = apiTranslator.entry.fromMap(map);

    if (await db.query(
      HasExistingOutgoingMutationForDownloadedEntry(
        entry: entry,
      ),
    )) {
      logger.i('sync:download Ignoring entry[${entry.remoteId!.dbValue}]'
          ' because it has existing outgoing mutation');
      return null;
    }

    bool projectExist = true;
    final projectIds = _extractProjectIds(map);
    for (final projectId in projectIds) {
      projectExist = projectExist &&
          (await db.query(
              HasExistingProjectForEntryRepositoryQuery(remoteId: projectId)));
    }
    if (!projectExist) {
      params.needsProjectResync = true;
      logger.i('sync:download Entry[${entry.remoteId!.dbValue}] references '
          'missing project[${projectIds.join(',')}]. Marking for project resync.');
    }

    final start = DateTime.now();
    final id = await db.entry.save(
      db.context(queryScope: QueryScope(orgId: organization.id)),
      entry,
    );
    logger.d('sync:download Saved entry[$id] '
        '[+${DateTime.now().difference(start).inMilliseconds}ms]');
    return id;
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    final entry = await db.query(EntryByRemoteIdRepositoryQuery(remoteId));
    if (entry != null) {
      await db.entry.delete(db.context(), entry.id!);
    }
  }

  List<RemoteId> _extractProjectIds(Map<String, dynamic> map) {
    final extension = map['extension'] as Map<String, dynamic>? ?? {};
    final remoteIds = <RemoteId>[];

    for (final key in ['source_project_id', 'dest_project_id']) {
      final id = extension[key];
      if (id != null) {
        remoteIds.add(RemoteId(id));
      }
    }

    if (remoteIds.isEmpty) {
      final fallbackId =
          extension['project_id'] ?? extension['default_project_id'];
      if (fallbackId != null) {
        remoteIds.add(RemoteId(fallbackId));
      }
    }
    return remoteIds;
  }
}
