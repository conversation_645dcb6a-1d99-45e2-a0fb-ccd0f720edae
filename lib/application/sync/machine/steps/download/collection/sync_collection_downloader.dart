import 'dart:async';

import 'package:bitacora/application/sync/machine/steps/download/collection/sync_metadata_repository_query.dart';
import 'package:bitacora/application/sync/machine/sync_machine_params.dart';
import 'package:bitacora/application/sync/pending_relations/sync_pending_relations_service.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/sync_metadata/sync_metadata.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_last_sync_time.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_next_page_token.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/widgets.dart';

abstract class SyncCollectionDownloader {
  final SyncMachineParams params;

  const SyncCollectionDownloader(this.params);

  Repository get db => params.db;

  Translator get apiTranslator => params.apiTranslator;

  Organization get organization => params.organization;

  SyncPendingRelationsService get pendingRelationsService =>
      SyncPendingRelationsService(db: db, organization: organization);

  @protected
  int get syncVersion => 1;

  DateTime? get startResyncTime => null;

  SyncMetadataCollectionType get collectionType;

  Set<SyncTriggerSource> get triggerResetSources => <SyncTriggerSource>{};

  @protected
  Future<LocalId?> translateAndSave(Map<String, dynamic> map);

  @protected
  Future<void> deleteArchived(RemoteId remoteId);

  Future<SyncMetadata> getSyncMetadata() async {
    final syncMetadata = await db.query(
          SyncMetadataRepositoryQuery(
            syncMetadataCollectionType: collectionType,
          ),
          context: db.context(queryScope: QueryScope(orgId: organization.id!)),
        ) ??
        SyncMetadata(
          collectionType: collectionType,
          syncVersion: SyncMetadataSyncVersion(1),
        );

    if (syncVersion == syncMetadata.syncVersion!.value) {
      return syncMetadata;
    }

    await db.syncMetadata.save(
      db.context(),
      SyncMetadata(
        organization: Organization(id: organization.id!),
        collectionType: collectionType,
        syncVersion: SyncMetadataSyncVersion(syncVersion),
      ),
    );

    return SyncMetadata(
      collectionType: collectionType,
      lastSyncTime: SyncMetadataLastSyncTime(startResyncTime),
      nextPageToken: SyncMetadataNextPageToken(null),
      syncVersion: SyncMetadataSyncVersion(syncVersion),
      organization: params.organization,
    );
  }

  Future<void> download(
    Map<String, dynamic> data,
    SyncMetadataLastSyncTime lastSyncTime,
  ) async {
    final archivedResourcesData = data['archived_items'];
    if (archivedResourcesData != null) {
      for (final id in archivedResourcesData) {
        logger.i('sync:download:${collectionType.value.apiKey} archive $id');
        await deleteArchived(RemoteId(id));
      }
    }

    final itemsData = data['items'];
    for (final itemData in itemsData) {
      try {
        final start = DateTime.now();
        final id = await translateAndSave(itemData);
        logger.d(_wrapSyncLog(
          'Saved [$id] '
          '[+${DateTime.now().difference(start).inMilliseconds}ms]',
        ));
      } catch (e, s) {
        logger.f(_wrapSyncLog('Failed to save $e\n$s'));
      }
    }
    final nextSyncNextPageToken =
        SyncMetadataNextPageToken(data['next_page_token']);
    await db.syncMetadata.save(
      db.context(),
      SyncMetadata(
        organization: Organization(id: organization.id!),
        collectionType: collectionType,
        nextPageToken: nextSyncNextPageToken,
        lastSyncTime: lastSyncTime,
        syncVersion: SyncMetadataSyncVersion(syncVersion),
      ),
    );
  }

  Future<void> resolvePendingRelations() async {}

  String _wrapSyncLog(String s) =>
      'sync:download:${collectionType.value.apiKey}${params.durationTracker} $s';
}
