import 'package:bitacora/application/cache/organization/organization_cache_repository_query.dart';
import 'package:bitacora/application/cache/project/project_cache_db_query.dart';
import 'package:bitacora/dev/db_tools/sync_download_project_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/upload/sync_outgoing_mutation_repository_query.dart';
import 'package:bitacora/dev/dev_log_dumper.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entries_repository_query.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/limit_offset_cursor.dart';
import 'package:flutter/material.dart';

class DevDbSpeedTest extends StatefulWidget {
  final LogDumper logDumper;

  const DevDbSpeedTest({
    super.key,
    required this.logDumper,
  });

  @override
  State<DevDbSpeedTest> createState() => _DevDbSpeedTestState();
}

class _DevDbSpeedTestState extends State<DevDbSpeedTest> {
  int _numRuns = 0;
  double _averageResult = 0;
  Duration _lastResult = const Duration();

  int _repeatCount = 0;
  bool _isBusy = false;

  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed: () async {
          if (_isBusy) {
            _repeatCount++;
            return;
          }
          await _runTest();
        },
        child: Row(
          children: [
            const Icon(Icons.speed),
            const SizedBox(width: 8),
            Text('Db Speed Test ${_getFormattedResults()}'),
          ],
        ));
  }

  Future<void> _runTest() async {
    _isBusy = true;
    widget.logDumper.clear();
    final start = DateTime.now();
    await runDbSpeedTest(widget.logDumper, run: '${_numRuns + 1}');
    final end = DateTime.now();

    setState(() {
      _numRuns++;
      _lastResult = end.difference(start);

      final x = _averageResult * ((_numRuns - 1) / _numRuns);
      final y = _lastResult.inMilliseconds * (1 / _numRuns);
      _averageResult = x + y;
      widget.logDumper.log(_getFormattedResults());
    });

    if (_repeatCount > 0) {
      _repeatCount--;
      await _runTest();
    } else {
      _isBusy = false;
    }
  }

  String _getFormattedResults() {
    if (_lastResult == const Duration()) {
      return '';
    }
    return '-> ${_lastResult.inMilliseconds}ms '
        '($_numRuns, ${_averageResult.toInt()}ms)';
  }
}

Future<void> runDbSpeedTest(LogDumper logDumper, {String run = ''}) async {
  const isGhostEntriesEnabled = false;

  logDumper.log('Db Speed Test', isLap: true);
  logDumper.log('Creating db');
  final db = await _nukeAndCreateDb(logDumper, run);

  final owner = User(
    name: UserName('Waluigi Fernández $run'),
    email: const UserEmail('<EMAIL>'),
  );
  final ownerId = await db.user.save(db.context(), owner);
  final ownerWithId = User(id: ownerId, email: owner.email);

  logDumper.log('Writing db');
  await createMockOrg(
    db,
    ownerWithId,
    logDumper,
    numProjects: 15,
    numUsers: 50,
    numEntries: 200,
    numOpenEntries: 100,
    randomSeed: 1,
  );

  logDumper.log('Reading db');
  final savedOrgs = await db.query(const OrganizationCacheRepositoryQuery());
  final savedOrg = savedOrgs[0];
  logDumper.log('Queried organization: ${savedOrg.name!.displayValue}');

  final context = db.context(
      queryScope: db.queryScope(orgId: savedOrg.id, userId: ownerId));
  final projectCache = await db.query(ProjectCacheDbQuery(savedOrg.id!),
      context: db.context(queryScope: QueryScope(userId: ownerId)));
  logDumper.log('Queried project cache: ${projectCache.length}');

  final projectsForSync = await db.query(
    SyncDownloadProjectsRepositoryQuery(organizationId: savedOrg.id!),
    context: context,
  );
  logDumper.log('Queried project for sync: ${projectsForSync.length}');

  final nextOutgoingMutation = await db.query(
    const SyncOutgoingMutationRepositoryQuery(),
    context: context,
  );
  logDumper.log('Queried entry for sync: $nextOutgoingMutation');

  var nEntries = 0;

  final openEntries = await db.query(
    const LogListEntriesRepositoryQuery(isOpenEntries: true),
    context: context.copyWith(cursor: const LimitOffsetCursor(1000, 0)),
  );
  nEntries += openEntries.length;
  logDumper.log('Queried open entries: ${openEntries.length}');

  final firstDay = await db.entry
      .findNextLogDay(context, const LogDay(0), 1, isGhostEntriesEnabled);
  final lastDay = await db.entry.findNextLogDay(
    context,
    const LogDay(99999999),
    -1,
    isGhostEntriesEnabled,
  );
  logDumper.log('Queried first day:${firstDay!.displayValue}'
      ' last day:${lastDay!.displayValue}');

  var day = firstDay;
  while (true) {
    final daylog = await db.query(
      LogListEntriesRepositoryQuery(
        day: day,
        isGhostEntriesEnabled: isGhostEntriesEnabled,
      ),
      context: context.copyWith(cursor: const LimitOffsetCursor(1000, 0)),
    );
    nEntries += daylog.length;
    logDumper.log('Queried daylog ${day.displayValue}: '
        '${daylog.length} entries');
    day = LogDay(getLogDayAfter(day.value, 1));
    if (day.value > lastDay.value) {
      break;
    }
  }
  logDumper.log('Total entries read: $nEntries');
  logDumper.log('Database read', isLap: true);

  logDumper.log('Nuking database');
  await db.nuke();
  logDumper.log('Database Nuked', isLap: true);

  logDumper.log('Done', isLast: true);
}

Future<DbRepository> _nukeAndCreateDb(LogDumper logDumper, String run) async {
  final fileName = 'speedTest$run.db';
  final dbToNuke = DbRepository(file: fileName);
  await dbToNuke.nuke();
  logDumper.log('Db pre-nuked');

  final db = DbRepository(file: fileName);
  logDumper.log('Registered queries');

  await db.context().executor;
  logDumper.log('Db opened');
  logDumper.log('Database created', isLap: true);

  return db;
}
