import 'package:bitacora/domain/common/value_object/value_object.dart';

enum EntrySourceTypeValue {
  mobile('MOBILE', 1),
  mobileQr('MOBILE_QR', 2),
  mobileAi('MOBILE_AI', 3),
  web('WEB', 4),
  sharedForm('SHARED_FORM', 5);

  final String apiValue;
  final int dbValue;

  const EntrySourceTypeValue(this.apiValue, this.dbValue);

  factory EntrySourceTypeValue.fromDbValue(dbValue) {
    switch (dbValue) {
      case 1:
        return EntrySourceTypeValue.mobile;
      case 2:
        return EntrySourceTypeValue.mobileQr;
      case 3:
        return EntrySourceTypeValue.mobileAi;
      case 4:
        return EntrySourceTypeValue.web;
      case 5:
        return EntrySourceTypeValue.sharedForm;
      default:
        throw 'EntrySourceTypeValue dbValue not supported';
    }
  }

  factory EntrySourceTypeValue.fromApiValue(apiValue) {
    switch (apiValue) {
      case 'MOBILE':
        return EntrySourceTypeValue.mobile;
      case 'MOBILE_QR':
        return EntrySourceTypeValue.mobileQr;
      case 'MOBILE_AI':
        return EntrySourceTypeValue.mobileAi;
      case 'WEB':
        return EntrySourceTypeValue.mobileAi;
      case 'SHARED_FORM':
        return EntrySourceTypeValue.sharedForm;
      default:
        throw 'EntrySourceTypeValue apiValue[$apiValue] not supported';
    }
  }
}

class EntrySourceType extends ValueObject<EntrySourceTypeValue> {
  static const EntrySourceType mobile =
      EntrySourceType(EntrySourceTypeValue.mobile);
  static const EntrySourceType mobileQr =
      EntrySourceType(EntrySourceTypeValue.mobileQr);
  static const EntrySourceType mobileAi =
      EntrySourceType(EntrySourceTypeValue.mobileAi);
  static const EntrySourceType web = EntrySourceType(EntrySourceTypeValue.web);
  static const EntrySourceType sharedForm = EntrySourceType(EntrySourceTypeValue.sharedForm);

  const EntrySourceType(super.value);

  factory EntrySourceType.fromDbValue(int dbValue) =>
      EntrySourceType(EntrySourceTypeValue.fromDbValue(dbValue));

  factory EntrySourceType.fromApiValue(String apiValue) =>
      EntrySourceType(EntrySourceTypeValue.fromApiValue(apiValue));

  @override
  int get dbValue => value.dbValue;

  @override
  String get apiValue => value.apiValue;
}
