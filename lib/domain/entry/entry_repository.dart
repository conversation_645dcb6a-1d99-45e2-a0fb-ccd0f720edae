import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/entry_fields_builder.dart';
import 'package:bitacora/domain/entry/filter/entry_filter.dart';
import 'package:bitacora/domain/extension/extension_type.dart';

abstract class EntryRepository<C extends RepositoryQueryContext,
    F extends EntryFieldsBuilder> extends RepositoryTable<Entry, C, F> {
  Future<List<Entry>> findUnsetPermissionCache(
    C context,
    LogDay? minDay,
    LogDay? maxDay,
  );

  Future<Entry?> findLast(C context);

  Future<List<Entry>> findDaylog(
    C context,
    LogDay day,
    bool isGhostEntriesEnabled, [
    bool excludeProgresslog = false,
  ]);

  Future<List<Entry>> findAllInWeek(C context, LogDay lastDayInWeek);

  Future<List<Entry>> findOpenEntries(C context, {LocalId? assigneeId});

  Future<LogDay?> findNextLogDay(
    C context,
    LogDay day,
    int direction,
    bool isGhostEntriesEnabled,
  );

  Future<Entry?> findCheckInFromEntry(C context, Entry entry);

  Future<List<Entry>> findByCreatedAt(C context, EntryCreatedAt createdAt);

  Future<List<Entry>> incompleteOpenEntries(C context);

  Future<Entry?> findWithStartedTimer(C context, [LocalId? entryId]);

  Future<void> cleanUp(C context);

  Future<void> setAccessCache(C context, Iterable<LocalId> ids);

  Future<List<String>> assignees(C context);

  Future<Map<LogDay, int>> getEntryCountByMonth(C context, DateTime date);

  Future<Map<LogDay, int>> getEntryCountByWeek(C context, DateTime date);

  Future<Entry?> findByLocationTrackingId(
      C context, LocalId locationTrackingId);

  Future<Entry?> findByExtension(
      C context, ExtensionType extensionType, LocalId extensionId);

  Future<List<Entry>> findByEntryGroup(C context, LocalId entryGroupId);

  Future<List<Entry>> searchAll(
      C context, String pattern, EntryFilter entryFilter);
}
